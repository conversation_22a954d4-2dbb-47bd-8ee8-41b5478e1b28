{"nodes": {"agenix": {"inputs": {"darwin": "darwin", "home-manager": "home-manager", "nixpkgs": "nixpkgs", "systems": "systems"}, "locked": {"lastModified": 1750173260, "narHash": "sha256-9P1FziAwl5+3edkfFcr5HeGtQUtrSdk/MksX39GieoA=", "owner": "ryantm", "repo": "agenix", "rev": "531beac616433bac6f9e2a19feb8e99a22a66baf", "type": "github"}, "original": {"owner": "ryantm", "repo": "agenix", "type": "github"}}, "base16": {"inputs": {"fromYaml": "fromYaml"}, "locked": {"lastModified": 1746562888, "narHash": "sha256-YgNJQyB5dQiwavdDFBMNKk1wyS77AtdgDk/VtU6wEaI=", "owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "base16.nix", "rev": "806a1777a5db2a1ef9d5d6f493ef2381047f2b89", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "base16.nix", "type": "github"}}, "base16-fish": {"flake": false, "locked": {"lastModified": 1622559957, "narHash": "sha256-PebymhVYbL8trDVVXxCvZgc0S5VxI7I1Hv4RMSquTpA=", "owner": "<PERSON><PERSON><PERSON>", "repo": "base16-fish", "rev": "2f6dd973a9075dabccd26f1cded09508180bf5fe", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON>", "repo": "base16-fish", "type": "github"}}, "base16-helix": {"flake": false, "locked": {"lastModified": 1748408240, "narHash": "sha256-9M2b1rMyMzJK0eusea0x3lyh3mu5nMeEDSc4RZkGm+g=", "owner": "tinted-theming", "repo": "base16-helix", "rev": "6c711ab1a9db6f51e2f6887cc3345530b33e152e", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "base16-helix", "type": "github"}}, "base16-vim": {"flake": false, "locked": {"lastModified": 1732806396, "narHash": "sha256-e0bpPySdJf0F68Ndanwm+KWHgQiZ0s7liLhvJSWDNsA=", "owner": "tinted-theming", "repo": "base16-vim", "rev": "577fe8125d74ff456cf942c733a85d769afe58b7", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "base16-vim", "rev": "577fe8125d74ff456cf942c733a85d769afe58b7", "type": "github"}}, "crane": {"locked": {"lastModified": 1748970125, "narHash": "sha256-UDyigbDGv8fvs9aS95yzFfOKkEjx1LO3PL3DsKopohA=", "owner": "<PERSON><PERSON><PERSON>", "repo": "crane", "rev": "323b5746d89e04b22554b061522dfce9e4c49b18", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON>", "repo": "crane", "type": "github"}}, "darwin": {"inputs": {"nixpkgs": ["agenix", "nixpkgs"]}, "locked": {"lastModified": 1744478979, "narHash": "sha256-dyN+teG9G82G+m+PX/aSAagkC+vUv0SgUw3XkPhQodQ=", "owner": "lnl7", "repo": "nix-darwin", "rev": "43975d782b418ebf4969e9ccba82466728c2851b", "type": "github"}, "original": {"owner": "lnl7", "ref": "master", "repo": "nix-darwin", "type": "github"}}, "disko": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1750040002, "narHash": "sha256-KrC9iOVYIn6ukpVlHbqSA4hYCZ6oDyJKrcLqv4c5v84=", "owner": "nix-community", "repo": "disko", "rev": "7f1857b31522062a6a00f88cbccf86b43acceed1", "type": "github"}, "original": {"owner": "nix-community", "repo": "disko", "type": "github"}}, "firefox-gnome-theme": {"flake": false, "locked": {"lastModified": 1748383148, "narHash": "sha256-pGvD/RGuuPf/4oogsfeRaeMm6ipUIznI2QSILKjKzeA=", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "repo": "firefox-gnome-theme", "rev": "4eb2714fbed2b80e234312611a947d6cb7d70caf", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "repo": "firefox-gnome-theme", "type": "github"}}, "flake-compat": {"flake": false, "locked": {"lastModified": 1747046372, "narHash": "sha256-CIVLLkVgvHYbgI2UpXvIIBJ12HWgX+fjA8Xf8PUmqCY=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "9100a0f413b0c601e0533d1d94ffd501ce2e7885", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-compat_2": {"locked": {"lastModified": 1747046372, "narHash": "sha256-CIVLLkVgvHYbgI2UpXvIIBJ12HWgX+fjA8Xf8PUmqCY=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "9100a0f413b0c601e0533d1d94ffd501ce2e7885", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-parts": {"inputs": {"nixpkgs-lib": ["lanzaboote", "nixpkgs"]}, "locked": {"lastModified": 1749398372, "narHash": "sha256-tYBdgS56eXYaWVW3fsnPQ/nFlgWi/Z2Ymhyu21zVM98=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "9305fe4e5c2a6fcf5ba6a3ff155720fbe4076569", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "flake-parts_2": {"inputs": {"nixpkgs-lib": ["stylix", "nixpkgs"]}, "locked": {"lastModified": 1743550720, "narHash": "sha256-hIshGgKZCgWh6AYJpJmRgFdR3WUbkY04o82X05xqQiY=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "c621e8422220273271f52058f618c94e405bb0f5", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "flake-utils": {"inputs": {"systems": "systems_2"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "flake-utils_2": {"inputs": {"systems": "systems_3"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "flake-utils_3": {"inputs": {"systems": "systems_4"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "flakey-profile": {"locked": {"lastModified": 1712898590, "narHash": "sha256-FhGIEU93VHAChKEXx905TSiPZKga69bWl1VB37FK//I=", "owner": "lf-", "repo": "flakey-profile", "rev": "243c903fd8eadc0f63d205665a92d4df91d42d9d", "type": "github"}, "original": {"owner": "lf-", "repo": "flakey-profile", "type": "github"}}, "fromYaml": {"flake": false, "locked": {"lastModified": 1731966426, "narHash": "sha256-lq95WydhbUTWig/JpqiB7oViTcHFP8Lv41IGtayokA8=", "owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "fromYaml", "rev": "106af9e2f715e2d828df706c386a685698f3223b", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "fromYaml", "type": "github"}}, "git-hooks": {"inputs": {"flake-compat": ["stylix", "flake-compat"], "gitignore": "gitignore_2", "nixpkgs": ["stylix", "nixpkgs"]}, "locked": {"lastModified": 1747372754, "narHash": "sha256-2Y53NGIX2vxfie1rOW0Qb86vjRZ7ngizoo+bnXU9D9k=", "owner": "cachix", "repo": "git-hooks.nix", "rev": "80479b6ec16fefd9c1db3ea13aeb038c60530f46", "type": "github"}, "original": {"owner": "cachix", "repo": "git-hooks.nix", "type": "github"}}, "gitignore": {"inputs": {"nixpkgs": ["lanzaboote", "pre-commit-hooks-nix", "nixpkgs"]}, "locked": {"lastModified": 1709087332, "narHash": "sha256-HG2cCnktfHsKV0s4XW83gU3F57gaTljL9KNSuG6bnQs=", "owner": "hercules-ci", "repo": "gitignore.nix", "rev": "637db329424fd7e46cf4185293b9cc8c88c95394", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "gitignore.nix", "type": "github"}}, "gitignore_2": {"inputs": {"nixpkgs": ["stylix", "git-hooks", "nixpkgs"]}, "locked": {"lastModified": 1709087332, "narHash": "sha256-HG2cCnktfHsKV0s4XW83gU3F57gaTljL9KNSuG6bnQs=", "owner": "hercules-ci", "repo": "gitignore.nix", "rev": "637db329424fd7e46cf4185293b9cc8c88c95394", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "gitignore.nix", "type": "github"}}, "gnome-shell": {"flake": false, "locked": {"lastModified": 1744584021, "narHash": "sha256-0RJ4mJzf+klKF4Fuoc8VN8dpQQtZnKksFmR2jhWE1Ew=", "owner": "GNOME", "repo": "gnome-shell", "rev": "52c517c8f6c199a1d6f5118fae500ef69ea845ae", "type": "github"}, "original": {"owner": "GNOME", "ref": "48.1", "repo": "gnome-shell", "type": "github"}}, "home-manager": {"inputs": {"nixpkgs": ["agenix", "nixpkgs"]}, "locked": {"lastModified": 1745494811, "narHash": "sha256-YZCh2o9Ua1n9uCvrvi5pRxtuVNml8X2a03qIFfRKpFs=", "owner": "nix-community", "repo": "home-manager", "rev": "abfad3d2958c9e6300a883bd443512c55dfeb1be", "type": "github"}, "original": {"owner": "nix-community", "repo": "home-manager", "type": "github"}}, "home-manager_2": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1750263149, "narHash": "sha256-VYSGDzq4ds4LIYvyEYG+RBqGMGBZhES2tvnmU4TJBn0=", "owner": "nix-community", "repo": "home-manager", "rev": "85e68c6a388ef1dfc799aaa01f00758c58e87d89", "type": "github"}, "original": {"owner": "nix-community", "repo": "home-manager", "type": "github"}}, "home-manager_3": {"inputs": {"nixpkgs": ["stylix", "nixpkgs"]}, "locked": {"lastModified": 1748737919, "narHash": "sha256-5kvBbLYdp+n7Ftanjcs6Nv+UO6sBhelp6MIGJ9nWmjQ=", "owner": "nix-community", "repo": "home-manager", "rev": "5675a9686851d9626560052a032c4e14e533c1fa", "type": "github"}, "original": {"owner": "nix-community", "repo": "home-manager", "type": "github"}}, "jovian": {"inputs": {"nix-github-actions": "nix-github-actions", "nixpkgs": "nixpkgs_2"}, "locked": {"lastModified": 1750230721, "narHash": "sha256-rg/lnazeno/f4VNSv+t2Zwio/OyCYKx5zV9/8hfhfgA=", "owner": "jovian-experiments", "repo": "jovian-nixos", "rev": "6c88df8c85ad3f80a5832edc50534a5add255b47", "type": "github"}, "original": {"owner": "jovian-experiments", "ref": "development", "repo": "jovian-nixos", "type": "github"}}, "lanzaboote": {"inputs": {"crane": "crane", "flake-compat": "flake-compat", "flake-parts": "flake-parts", "nixpkgs": ["nixpkgs"], "pre-commit-hooks-nix": "pre-commit-hooks-nix", "rust-overlay": "rust-overlay"}, "locked": {"lastModified": 1750168384, "narHash": "sha256-PBfJ7dGsR02im/RYN8wXII8yNPFhKxiPdq+JDfbvD2k=", "owner": "nix-community", "repo": "lanzaboote", "rev": "38c2addd2e0cedcb03708de6e6c21fb1be86d410", "type": "github"}, "original": {"owner": "nix-community", "repo": "lanzaboote", "type": "github"}}, "lix": {"flake": false, "locked": {"lastModified": 1750191637, "narHash": "sha256-p+VryHLOoU2q1VnAnY4wOXk0PomYJ+1Zb3O0J5twRhU=", "ref": "refs/heads/main", "rev": "87d99da6ca50d63caec28513e23eeeb915781472", "revCount": 18059, "type": "git", "url": "https://git.lix.systems/lix-project/lix.git"}, "original": {"type": "git", "url": "https://git.lix.systems/lix-project/lix.git"}}, "lix-module": {"inputs": {"flake-utils": "flake-utils_2", "flakey-profile": "flakey-profile", "lix": ["lix"], "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1747667424, "narHash": "sha256-7EICjbmG6lApWKhFtwvZovdcdORY1CEe6/K7JwtpYfs=", "ref": "refs/heads/main", "rev": "3c23c6ae2aecc1f76ae7993efe1a78b5316f0700", "revCount": 144, "type": "git", "url": "https://git.lix.systems/lix-project/nixos-module.git"}, "original": {"type": "git", "url": "https://git.lix.systems/lix-project/nixos-module.git"}}, "mnw": {"locked": {"lastModified": 1748710831, "narHash": "sha256-eZu2yH3Y2eA9DD3naKWy/sTxYS5rPK2hO7vj8tvUCSU=", "owner": "gerg-l", "repo": "mnw", "rev": "cff958a4e050f8d917a6ff3a5624bc4681c6187d", "type": "github"}, "original": {"owner": "gerg-l", "repo": "mnw", "type": "github"}}, "nix-github-actions": {"inputs": {"nixpkgs": ["jovian", "nixpkgs"]}, "locked": {"lastModified": 1729697500, "narHash": "sha256-VFTWrbzDlZyFHHb1AlKRiD/qqCJIripXKiCSFS8fAOY=", "owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "nix-github-actions", "rev": "e418aeb728b6aa5ca8c5c71974e7159c2df1d8cf", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "ref": "matrix-name", "repo": "nix-github-actions", "type": "github"}}, "nix-github-actions_2": {"inputs": {"nixpkgs": ["run0-sudo-shim", "nixpkgs"]}, "locked": {"lastModified": 1737420293, "narHash": "sha256-F1G5ifvqTpJq7fdkT34e/Jy9VCyzd5XfJ9TO8fHhJWE=", "owner": "nix-community", "repo": "nix-github-actions", "rev": "f4158fa080ef4503c8f4c820967d946c2af31ec9", "type": "github"}, "original": {"owner": "nix-community", "repo": "nix-github-actions", "type": "github"}}, "nix-index-database": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1749960154, "narHash": "sha256-EWlr9MZDd+GoGtZB4QsDzaLyaDQPGnRY03MFp6u2wSg=", "owner": "nix-community", "repo": "nix-index-database", "rev": "424a40050cdc5f494ec45e46462d288f08c64475", "type": "github"}, "original": {"owner": "nix-community", "repo": "nix-index-database", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1745391562, "narHash": "sha256-sPwcCYuiEopaafePqlG826tBhctuJsLx/mhKKM5Fmjo=", "owner": "NixOS", "repo": "nixpkgs", "rev": "8a2f738d9d1f1d986b5a4cd2fd2061a7127237d7", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_2": {"locked": {"lastModified": 1747744144, "narHash": "sha256-W7lqHp0qZiENCDwUZ5EX/lNhxjMdNapFnbErcbnP11Q=", "owner": "NixOS", "repo": "nixpkgs", "rev": "2795c506fe8fb7b03c36ccb51f75b6df0ab2553f", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_3": {"locked": {"lastModified": 1750243412, "narHash": "sha256-ANe+tSw+MBk+WJjKvqEsQtmNmZkleeGA1GVsNdIVQvU=", "owner": "nixos", "repo": "nixpkgs", "rev": "4283f162aa9eec017b9f95ac061a4bf4606a3371", "type": "github"}, "original": {"owner": "nixos", "ref": "nixos-unstable-small", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_4": {"locked": {"lastModified": 1748460289, "narHash": "sha256-7doLyJBzCllvqX4gszYtmZUToxKvMUrg45EUWaUYmBg=", "owner": "NixOS", "repo": "nixpkgs", "rev": "96ec055edbe5ee227f28cdbc3f1ddf1df5965102", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nur": {"inputs": {"flake-parts": ["stylix", "flake-parts"], "nixpkgs": ["stylix", "nixpkgs"], "treefmt-nix": "treefmt-nix_2"}, "locked": {"lastModified": 1748730660, "narHash": "sha256-5LKmRYKdPuhm8j5GFe3AfrJL8dd8o57BQ34AGjJl1R0=", "owner": "nix-community", "repo": "NUR", "rev": "2c0bc52fe14681e9ef60e3553888c4f086e46ecb", "type": "github"}, "original": {"owner": "nix-community", "repo": "NUR", "type": "github"}}, "pre-commit-hooks-nix": {"inputs": {"flake-compat": ["lanzaboote", "flake-compat"], "gitignore": "gitignore", "nixpkgs": ["lanzaboote", "nixpkgs"]}, "locked": {"lastModified": 1749636823, "narHash": "sha256-WUaIlOlPLyPgz9be7fqWJA5iG6rHcGRtLERSCfUDne4=", "owner": "cachix", "repo": "pre-commit-hooks.nix", "rev": "623c56286de5a3193aa38891a6991b28f9bab056", "type": "github"}, "original": {"owner": "cachix", "repo": "pre-commit-hooks.nix", "type": "github"}}, "preservation": {"locked": {"lastModified": 1738541138, "narHash": "sha256-isT+jR8P8UFh5PJDzGHYXqVEHEZa0D5WvT5kfMf14AM=", "owner": "nix-community", "repo": "preservation", "rev": "2f16754f9f6b766c1429375ab7417dc81cc90a63", "type": "github"}, "original": {"owner": "nix-community", "repo": "preservation", "type": "github"}}, "root": {"inputs": {"agenix": "agenix", "disko": "disko", "flake-utils": "flake-utils", "home-manager": "home-manager_2", "jovian": "jovian", "lanzaboote": "lanzaboote", "lix": "lix", "lix-module": "lix-module", "mnw": "mnw", "nix-index-database": "nix-index-database", "nixpkgs": "nixpkgs_3", "preservation": "preservation", "run0-sudo-shim": "run0-sudo-shim", "stylix": "stylix"}}, "run0-sudo-shim": {"inputs": {"flake-utils": "flake-utils_3", "nix-github-actions": "nix-github-actions_2", "nixpkgs": ["nixpkgs"], "rust-overlay": "rust-overlay_2", "treefmt-nix": "treefmt-nix"}, "locked": {"lastModified": 1749833533, "narHash": "sha256-A3+EiyoVSafnLzyOv5blR2cExrHianqt4HNQ0mi7XuY=", "owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "run0-sudo-shim", "rev": "90e3388910476fae812421330ca6261e10792d6a", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "run0-sudo-shim", "type": "github"}}, "rust-overlay": {"inputs": {"nixpkgs": ["lanzaboote", "nixpkgs"]}, "locked": {"lastModified": 1749955444, "narHash": "sha256-CllTHvHX8KAdAZ+Lxzd23AmZTxO1Pfy+zC43/5tYkAE=", "owner": "oxalica", "repo": "rust-overlay", "rev": "539ba15741f0e6691a2448743dbc601d8910edce", "type": "github"}, "original": {"owner": "oxalica", "repo": "rust-overlay", "type": "github"}}, "rust-overlay_2": {"inputs": {"nixpkgs": ["run0-sudo-shim", "nixpkgs"]}, "locked": {"lastModified": 1746671794, "narHash": "sha256-V+mpk2frYIEm85iYf+KPDmCGG3zBRAEhbv0E3lHdG2U=", "owner": "oxalica", "repo": "rust-overlay", "rev": "ceec434b8741c66bb8df5db70d7e629a9d9c598f", "type": "github"}, "original": {"owner": "oxalica", "repo": "rust-overlay", "type": "github"}}, "stylix": {"inputs": {"base16": "base16", "base16-fish": "base16-fish", "base16-helix": "base16-helix", "base16-vim": "base16-vim", "firefox-gnome-theme": "firefox-gnome-theme", "flake-compat": "flake-compat_2", "flake-parts": "flake-parts_2", "git-hooks": "git-hooks", "gnome-shell": "gnome-shell", "home-manager": "home-manager_3", "nixpkgs": "nixpkgs_4", "nur": "nur", "systems": "systems_5", "tinted-foot": "tinted-foot", "tinted-kitty": "tinted-kitty", "tinted-schemes": "tinted-schemes", "tinted-tmux": "tinted-tmux", "tinted-zed": "tinted-zed"}, "locked": {"lastModified": 1750205637, "narHash": "sha256-49wV81h1jnHJky1XNHfgwxNA0oCwSTLMz4hhrtWCM8A=", "owner": "danth", "repo": "stylix", "rev": "82323751bcd45579c8d3a5dd05531c3c2a78e347", "type": "github"}, "original": {"owner": "danth", "repo": "stylix", "type": "github"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_2": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_3": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_4": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_5": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "tinted-foot": {"flake": false, "locked": {"lastModified": 1726913040, "narHash": "sha256-+eDZPkw7efMNUf3/Pv0EmsidqdwNJ1TaOum6k7lngDQ=", "owner": "tinted-theming", "repo": "tinted-foot", "rev": "fd1b924b6c45c3e4465e8a849e67ea82933fcbe4", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "tinted-foot", "rev": "fd1b924b6c45c3e4465e8a849e67ea82933fcbe4", "type": "github"}}, "tinted-kitty": {"flake": false, "locked": {"lastModified": 1735730497, "narHash": "sha256-4KtB+FiUzIeK/4aHCKce3V9HwRvYaxX+F1edUrfgzb8=", "owner": "tinted-theming", "repo": "tinted-kitty", "rev": "de6f888497f2c6b2279361bfc790f164bfd0f3fa", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "tinted-kitty", "type": "github"}}, "tinted-schemes": {"flake": false, "locked": {"lastModified": 1748180480, "narHash": "sha256-7n0XiZiEHl2zRhDwZd/g+p38xwEoWtT0/aESwTMXWG4=", "owner": "tinted-theming", "repo": "schemes", "rev": "87d652edd26f5c0c99deda5ae13dfb8ece2ffe31", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "schemes", "type": "github"}}, "tinted-tmux": {"flake": false, "locked": {"lastModified": 1748740859, "narHash": "sha256-OEM12bg7F4N5WjZOcV7FHJbqRI6jtCqL6u8FtPrlZz4=", "owner": "tinted-theming", "repo": "tinted-tmux", "rev": "57d5f9683ff9a3b590643beeaf0364da819aedda", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "tinted-tmux", "type": "github"}}, "tinted-zed": {"flake": false, "locked": {"lastModified": 1725758778, "narHash": "sha256-8P1b6mJWyYcu36WRlSVbuj575QWIFZALZMTg5ID/sM4=", "owner": "tinted-theming", "repo": "base16-zed", "rev": "122c9e5c0e6f27211361a04fae92df97940eccf9", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "base16-zed", "type": "github"}}, "treefmt-nix": {"inputs": {"nixpkgs": ["run0-sudo-shim", "nixpkgs"]}, "locked": {"lastModified": 1746216483, "narHash": "sha256-4h3s1L/kKqt3gMDcVfN8/4v2jqHrgLIe4qok4ApH5x4=", "owner": "numtide", "repo": "treefmt-nix", "rev": "29ec5026372e0dec56f890e50dbe4f45930320fd", "type": "github"}, "original": {"owner": "numtide", "repo": "treefmt-nix", "type": "github"}}, "treefmt-nix_2": {"inputs": {"nixpkgs": ["stylix", "nur", "nixpkgs"]}, "locked": {"lastModified": 1733222881, "narHash": "sha256-JIPcz1PrpXUCbaccEnrcUS8jjEb/1vJbZz5KkobyFdM=", "owner": "numtide", "repo": "treefmt-nix", "rev": "49717b5af6f80172275d47a418c9719a31a78b53", "type": "github"}, "original": {"owner": "numtide", "repo": "treefmt-nix", "type": "github"}}}, "root": "root", "version": 7}