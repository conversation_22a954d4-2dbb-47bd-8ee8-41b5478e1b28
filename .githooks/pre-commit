#!/usr/bin/env bash

STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACMR)

if ! nix run nixpkgs#nixpkgs-fmt -- .; then
    echo "nixpkgs-fmt failed with exit status $?"
    exit 1
fi

if ! nix run nixpkgs#statix -- check .; then
    echo "statix failed with exit status $?"
    exit 1
fi

MODIFIED_FILES=$(git status --porcelain | grep -v "^[? ]" | cut -c4-)
for file in $MODIFIED_FILES; do
    if ! echo "$STAGED_FILES" | grep -Fx "$file" > /dev/null; then
        echo "Warning: $file was modified but not staged. Not including in commit."
    fi
done

if ! echo "$STAGED_FILES" | xargs -r git add; then
    echo "Failed to re-stage files with exit status $?"
    exit 1
fi
