# Do not modify this file!  It was generated by ‘nixos-generate-config’
# and may be overwritten by future invocations.  Please make changes
# to /etc/nixos/configuration.nix instead.
{ config
, lib
, modulesPath
, ...
}:

{
  imports = [
    (modulesPath + "/installer/scan/not-detected.nix")
    ./disko.nix
  ];

  boot = {
    initrd.availableKernelModules = [
      "nvme"
      "xhci_pci"
      "ahci"
      "usb_storage"
      "usbhid"
      "uas"
      "sd_mod"
    ];
    initrd.kernelModules = [ ];
    kernelModules = [
      "kvm-amd"
      "amd_3d_vcache"
      "ntsync"
    ];
    extraModulePackages = [ ];
    kernelParams = [
      "drm.panic_screen=qr_code"
      "quiet"
    ];
    kernel.sysctl."kernel.sysrq" = 1;
  };

  fileSystems."/games" = {
    device = "/dev/disk/by-uuid/7d57a643-ac36-4e54-b873-0e19bd8a8645";
    fsType = "btrfs";
    options = [ "compress=zstd" ];
  };

  fileSystems."/backup" = {
    device = "/dev/disk/by-uuid/a4058013-b85c-4bf0-a187-b459a23267a9";
    fsType = "btrfs";
    options = [
      "compress=zstd"
      "nofail"
    ];
  };

  swapDevices = [
    {
      device = "/dev/disk/by-uuid/678b887c-a86e-449a-8f9a-3455505bd746";
      priority = -2;
    }
  ];

  # Enables DHCP on each ethernet and wireless interface. In case of scripted networking
  # (the default) this is the recommended approach. When using systemd-networkd it's
  # still possible to use this option, but it's recommended to use it in conjunction
  # with explicit per-interface declarations with `networking.interfaces.<interface>.useDHCP`.
  networking.useDHCP = lib.mkDefault true;
  # networking.interfaces.enp4s0.useDHCP = lib.mkDefault true;
  # networking.interfaces.wlp5s0f1u3.useDHCP = lib.mkDefault true;

  nixpkgs.hostPlatform = lib.mkDefault "x86_64-linux";
  hardware.cpu.amd.updateMicrocode = lib.mkDefault config.hardware.enableRedistributableFirmware;
}
