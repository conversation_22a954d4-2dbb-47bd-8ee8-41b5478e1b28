{ config, pkgs, lib, inputs, ... }:

{
  hardware = {
    amdgpu = {
      overdrive = {
        enable = true;
        ppfeaturemask = "0xffffffff";
      };
    };
    fancontrol = {
      enable = false;
      config = ''
        # Configuration file generated by pwmconfig, changes will be lost
        INTERVAL=10
        DEVPATH=hwmon5=devices/pci0000:00/0000:00:03.1/0000:08:00.0/0000:09:00.0/0000:0a:00.0 hwmon6=devices/platform/nct6775.656
        DEVNAME=hwmon5=amdgpu hwmon6=nct6798
        FCTEMPS=hwmon6/pwm7=hwmon6/temp7_input hwmon6/pwm6=hwmon6/temp6_input hwmon6/pwm5=hwmon6/temp5_input hwmon6/pwm4=hwmon6/temp4_input hwmon6/pwm3=hwmon6/temp3_input hwmon6/pwm2=hwmon6/temp2_input hwmon6/pwm1=hwmon6/temp1_input
        FCFANS=hwmon6/pwm7=hwmon5/fan1_input hwmon6/pwm6=hwmon5/fan1_input hwmon6/pwm5=hwmon5/fan1_input hwmon6/pwm4=hwmon6/fan4_input+hwmon5/fan1_input hwmon6/pwm3=hwmon6/fan3_input+hwmon5/fan1_input hwmon6/pwm2=hwmon6/fan2_input+hwmon5/fan1_input hwmon6/pwm1=hwmon6/fan1_input
        MINTEMP=hwmon6/pwm7=20 hwmon6/pwm6=20 hwmon6/pwm5=20 hwmon6/pwm4=20 hwmon6/pwm3=20 hwmon6/pwm2=20 hwmon6/pwm1=20
        MAXTEMP=hwmon6/pwm7=60 hwmon6/pwm6=60 hwmon6/pwm5=60 hwmon6/pwm4=60 hwmon6/pwm3=60 hwmon6/pwm2=60 hwmon6/pwm1=60
        MINSTART=hwmon6/pwm7=150 hwmon6/pwm6=150 hwmon6/pwm5=150 hwmon6/pwm4=150 hwmon6/pwm3=150 hwmon6/pwm2=150 hwmon6/pwm1=150
        MINSTOP=hwmon6/pwm7=100 hwmon6/pwm6=100 hwmon6/pwm5=100 hwmon6/pwm4=100 hwmon6/pwm3=100 hwmon6/pwm2=100 hwmon6/pwm1=0
      '';
    };
    graphics = {
      enable32Bit = true;
    };
  };
}
