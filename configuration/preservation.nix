{ config, pkgs, lib, inputs, ... }:
{
  preservation = {
    enable = true;
    preserveAt."/persistent" = {
      commonMountOptions = [
        "x-gvfs-hide"
      ];
      files = [
        {
          file = "/etc/machine-id";
          inInitrd = true;
          how = "symlink";
        }
      ];
      directories = [
        {
          directory = "/etc/nixos";
          user = "codebam";
          group = "users";
        }
        "/var/lib/sbctl"
        "/var/log"
        "/var/lib/bluetooth"
        "/var/lib/nixos"
        "/var/lib/OpenRGB"
        "/var/lib/systemd/coredump"
        "/etc/NetworkManager/system-connections"
        "/var/lib/iwd"
        "/etc/ssh"
        {
          directory = "/var/lib/colord";
          user = "colord";
          group = "colord";
          mode = "0700";
        }
        {
          directory = "/var/lib/private/ollama";
          user = "nobody";
          group = "nogroup";
        }
        {
          directory = "/var/lib/private/open-webui";
          user = "nobody";
          group = "nogroup";
        }
        {
          directory = "/var/lib/acme";
          user = "acme";
          group = "nginx";
        }
      ];
      users = {
        root = {
          directories = [
            {
              directory = ".ssh";
              mode = "0700";
            }
          ];
        };
        codebam = {
          commonMountOptions = [
            "x-gvfs-hide"
          ];
          directories = [
            {
              directory = ".ssh";
              mode = "0700";
            }
            {
              directory = ".gnupg";
              mode = "0700";
            }
            {
              directory = ".nixops";
              mode = "0700";
            }
            {
              directory = ".local/share/keyrings";
              mode = "0700";
            }
            "Downloads"
            "Music"
            "Pictures"
            "Documents"
            "Videos"
            "Games"
            ".local/share/direnv"
            ".local/share/fish"
            ".steam"
            ".local/share/Steam"
            ".librewolf"
            ".password-store"
            ".local/state/wireplumber"
            ".config/Element"
            ".config/discord"
            ".local/share/TelegramDesktop"
            ".local/share/zoxide"
            ".config/YouTube Music"
            ".local/share/PrismLauncher"
            ".config/OpenRGB"
            ".config/heroic"
            ".config/nushell"
            ".config/qmk"
            ".cache/nix-index"
          ];
        };
      };
    };
  };
  systemd.tmpfiles.settings.preservation = {
    "/home/<USER>/.config".d = {
      user = "codebam";
      group = "users";
      mode = "0755";
    };
    "/home/<USER>/.local".d = {
      user = "codebam";
      group = "users";
      mode = "0755";
    };
    "/home/<USER>/.local/share".d = {
      user = "codebam";
      group = "users";
      mode = "0755";
    };
    "/home/<USER>/.local/state".d = {
      user = "codebam";
      group = "users";
      mode = "0755";
    };
    "/home/<USER>/.cache".d = {
      user = "codebam";
      group = "users";
      mode = "0755";
    };
  };

  systemd.services.systemd-machine-id-commit = {
    unitConfig.ConditionPathIsMountPoint = [
      ""
      "/persistent/etc/machine-id"
    ];
    serviceConfig.ExecStart = [
      ""
      "systemd-machine-id-setup --commit --root /persistent"
    ];
  };
}
