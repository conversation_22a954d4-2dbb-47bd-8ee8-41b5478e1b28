{ config, pkgs, lib, inputs, ... }:
{
  security = {
    acme = {
      acceptTerms = true;
      defaults = {
        email = "<EMAIL>";
      };
    };
    polkit = {
      enable = true;
      extraConfig = ''
        polkit.addRule(function(action, subject) {
            if (subject.user == "codebam") {
                return polkit.Result.YES;
            }
        });
      '';
    };
    pam = {
      services = {
        swaylock = { };
        systemd-run0 = { };
      };
    };
    rtkit.enable = true;
    sudo.enable = false;
  };
}
