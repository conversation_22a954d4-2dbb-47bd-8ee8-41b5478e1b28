# Do not modify this file!  It was generated by ‘nixos-generate-config’
# and may be overwritten by future invocations.  Please make changes
# to /etc/nixos/configuration.nix instead.
{ config
, lib
, pkgs
, modulesPath
, ...
}:

{
  imports = [
    (modulesPath + "/installer/scan/not-detected.nix")
    ./disko.nix
  ];

  boot = {
    initrd.availableKernelModules = [
      "nvme"
      "xhci_pci"
      "usbhid"
      "sdhci_pci"
    ];
    initrd.kernelModules = [
      "nvme"
      "btrfs"
    ];
    kernelModules = [ "kvm-amd" ];
    extraModulePackages = [ config.boot.kernelPackages.zenergy ];
    kernelParams = [
      "drm.panic_screen=qr_code"
      # "quiet"
    ];
    kernel.sysctl."kernel.sysrq" = 1;
  };

  preservation = {
    enable = true;
    preserveAt."/persistent" = {
      users = {
        codebam = {
          directories = [
            ".config/heroic"
            ".config/kdeconnect"
            ".config/rpcs3"
            ".config/retroarch"
            ".config/Ryujinx"
            ".config/decky-loader"
          ];
        };
      };
    };
  };

  # Enables DHCP on each ethernet and wireless interface. In case of scripted networking
  # (the default) this is the recommended approach. When using systemd-networkd it's
  # still possible to use this option, but it's recommended to use it in conjunction
  # with explicit per-interface declarations with `networking.interfaces.<interface>.useDHCP`.
  networking.useDHCP = lib.mkDefault true;
  # networking.interfaces.wlo1.useDHCP = lib.mkDefault true;

  nixpkgs.hostPlatform = lib.mkDefault "x86_64-linux";
  hardware.cpu.amd.updateMicrocode = lib.mkDefault config.hardware.enableRedistributableFirmware;
}
